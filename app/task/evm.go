package task

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"math/big"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"bepusdt/app/conf"
	"bepusdt/app/help"
	"bepusdt/app/log"
	"bepusdt/app/model"

	"github.com/panjf2000/ants/v2"
	"github.com/shopspring/decimal"
	"github.com/smallnest/chanx"
	"github.com/tidwall/gjson"
)

const (
	blockParseMaxNum    = 10                                                                   // 每次解析区块的最大数量
	evmTransferEvent    = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef" // ERC20 Transfer事件签名
	httpRequestTimeout  = time.Second * 5                                                      // HTTP请求超时时间
	httpContentType     = "application/json"                                                   // HTTP请求内容类型
	addressPrefixLength = 26                                                                   // 地址前缀长度（用于从topics中提取地址）
	hexDataPrefix       = "0x"                                                                 // 十六进制数据前缀
	successTxStatus     = "0x1"                                                                // 成功交易状态
	minTopicsLength     = 3                                                                    // Transfer事件最少topics数量
	goroutinePoolSize   = 2                                                                    // goroutine池大小
)

var chainBlockNum sync.Map
var contractMap = map[string]string{
	conf.UsdtXlayer:   model.OrderTradeTypeUsdtXlayer,
	conf.UsdtBep20:    model.OrderTradeTypeUsdtBep20,
	conf.UsdtPolygon:  model.OrderTradeTypeUsdtPolygon,
	conf.UsdtArbitrum: model.OrderTradeTypeUsdtArbitrum,
	conf.UsdtErc20:    model.OrderTradeTypeUsdtErc20,
	conf.UsdcErc20:    model.OrderTradeTypeUsdcErc20,
	conf.UsdcPolygon:  model.OrderTradeTypeUsdcPolygon,
	conf.UsdcXlayer:   model.OrderTradeTypeUsdcXlayer,
	conf.UsdcArbitrum: model.OrderTradeTypeUsdcArbitrum,
	conf.UsdcBep20:    model.OrderTradeTypeUsdcBep20,
	conf.UsdcBase:     model.OrderTradeTypeUsdcBase,
}
var networkTokenMap = map[string][]string{
	conf.Bsc:      {model.OrderTradeTypeUsdtBep20, model.OrderTradeTypeUsdcBep20},
	conf.Xlayer:   {model.OrderTradeTypeUsdtXlayer, model.OrderTradeTypeUsdcXlayer},
	conf.Polygon:  {model.OrderTradeTypeUsdtPolygon, model.OrderTradeTypeUsdcPolygon},
	conf.Arbitrum: {model.OrderTradeTypeUsdtArbitrum, model.OrderTradeTypeUsdcArbitrum},
	conf.Ethereum: {model.OrderTradeTypeUsdtErc20, model.OrderTradeTypeUsdcErc20},
	conf.Base:     {model.OrderTradeTypeUsdcBase},
	conf.Solana:   {model.OrderTradeTypeUsdtSolana, model.OrderTradeTypeUsdcSolana},
	conf.Aptos:    {model.OrderTradeTypeUsdtAptos, model.OrderTradeTypeUsdcAptos},
}
var client = &http.Client{Timeout: time.Second * 30}
var decimals = map[string]int32{
	conf.UsdtXlayer:   conf.UsdtXlayerDecimals,
	conf.UsdtBep20:    conf.UsdtBscDecimals,
	conf.UsdtPolygon:  conf.UsdtPolygonDecimals,
	conf.UsdtArbitrum: conf.UsdtArbitrumDecimals,
	conf.UsdtErc20:    conf.UsdtEthDecimals,
	conf.UsdcErc20:    conf.UsdcEthDecimals,
	conf.UsdcPolygon:  conf.UsdcPolygonDecimals,
	conf.UsdcXlayer:   conf.UsdcXlayerDecimals,
	conf.UsdcArbitrum: conf.UsdcArbitrumDecimals,
	conf.UsdcBep20:    conf.UsdcBscDecimals,
	conf.UsdcBase:     conf.UsdcBaseDecimals,
	conf.UsdcAptos:    conf.UsdcAptosDecimals,
	conf.UsdtAptos:    conf.UsdtAptosDecimals,
}

// jsonRPCRequest 封装JSON-RPC请求参数
type jsonRPCRequest struct {
	endpoint string
	payload  []byte
	timeout  time.Duration
}

// blockTimestamp 封装区块时间戳信息
type blockTimestamp map[string]time.Time

type block struct {
	InitStartOffset int64 // 首次偏移量，第一次启动时，区块高度需要叠加此值，设置为负值可解决部分已创建但未超时(未扫描)的订单问题
	RollDelayOffset int64 // 延迟偏移量，某些RPC节点如果不延迟，会报错 block is out of range，目前发现 https://rpc.xlayer.tech/ 存在此问题
	ConfirmedOffset int64 // 确认偏移量，开启交易确认后，区块高度需要减去此值认为交易已确认
}

type evm struct {
	Network        string
	Endpoint       string
	Block          block
	blockScanQueue *chanx.UnboundedChan[evmBlock]
}

type evmBlock struct {
	From int64
	To   int64
}

func init() {
	//register(task{callback: evmBlockDispatch})
}

// makeJSONRPCRequest 统一的JSON-RPC请求处理函数
func makeJSONRPCRequest(ctx context.Context, req jsonRPCRequest) ([]byte, error) {
	httpReq, err := http.NewRequestWithContext(ctx, "POST", req.endpoint, bytes.NewBuffer(req.payload))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", httpContentType)
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	return body, nil
}

// formatEvmAddress 格式化EVM地址（从topics中提取）
func formatEvmAddress(topicHex string) string {
	if len(topicHex) <= addressPrefixLength {
		return topicHex
	}
	return fmt.Sprintf("%s%s", hexDataPrefix, topicHex[addressPrefixLength:])
}

// parseHexAmount 解析十六进制金额
func parseHexAmount(hexStr string) (*big.Int, bool) {
	if !strings.HasPrefix(hexStr, hexDataPrefix) {
		return nil, false
	}
	amount, ok := big.NewInt(0).SetString(hexStr[2:], 16)
	return amount, ok && amount.Sign() > 0
}

// handleBlockError 统一的区块处理错误处理
func (e *evm) handleBlockError(blockRange evmBlock, errMsg string, err error) {
	conf.SetBlockFail(e.Network)
	e.blockScanQueue.In <- blockRange
	if err != nil {
		log.Warn(fmt.Sprintf("%s %s: %v", e.Network, errMsg, err))
	} else {
		log.Warn(fmt.Sprintf("%s %s", e.Network, errMsg))
	}
}

// blockRoll 获取最新区块高度并分发扫描任务
func (e *evm) blockRoll(ctx context.Context) {
	if rollBreak(e.Network) {
		return
	}

	currentBlockHeight, err := e.getCurrentBlockHeight(ctx)
	if err != nil {
		log.Warn(fmt.Sprintf("%s 获取当前区块高度失败: %v", e.Network, err))
		return
	}

	if currentBlockHeight <= 0 {
		return
	}

	// 应用确认偏移量
	if conf.GetTradeIsConfirmed() {
		currentBlockHeight -= e.Block.ConfirmedOffset
	}

	lastProcessedBlock := e.getLastProcessedBlock()

	// 检查区块高度差异是否过大
	if currentBlockHeight-lastProcessedBlock > conf.BlockHeightMaxDiff {
		lastProcessedBlock = e.blockInitOffset(currentBlockHeight, e.Block.InitStartOffset) - 1
	}

	chainBlockNum.Store(e.Network, currentBlockHeight)

	if currentBlockHeight <= lastProcessedBlock {
		return
	}

	// 分发区块扫描任务
	e.dispatchBlockScanTasks(lastProcessedBlock+1, currentBlockHeight)
}

// getCurrentBlockHeight 获取当前区块高度
func (e *evm) getCurrentBlockHeight(ctx context.Context) (int64, error) {
	payload := []byte(`{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}`)
	req := jsonRPCRequest{
		endpoint: e.Endpoint,
		payload:  payload,
		timeout:  httpRequestTimeout,
	}

	body, err := makeJSONRPCRequest(ctx, req)
	if err != nil {
		return 0, err
	}

	result := gjson.ParseBytes(body)
	blockHeight := help.HexStr2Int(result.Get("result").String()).Int64() - e.Block.RollDelayOffset
	return blockHeight, nil
}

// getLastProcessedBlock 获取上次处理的区块高度
func (e *evm) getLastProcessedBlock() int64 {
	if value, exists := chainBlockNum.Load(e.Network); exists {
		return value.(int64)
	}
	return 0
}

// dispatchBlockScanTasks 分发区块扫描任务
func (e *evm) dispatchBlockScanTasks(fromBlock, toBlock int64) {
	for from := fromBlock; from <= toBlock; from += blockParseMaxNum {
		to := from + blockParseMaxNum - 1
		if to > toBlock {
			to = toBlock
		}
		e.blockScanQueue.In <- evmBlock{From: from, To: to}
	}
}

func (e *evm) blockInitOffset(now, offset int64) int64 {
	go func() {
		ticker := time.NewTicker(time.Second)
		defer ticker.Stop()

		for b := now; b > now+offset; b -= blockParseMaxNum {
			if rollBreak(e.Network) {

				return
			}

			e.blockScanQueue.In <- evmBlock{From: b - blockParseMaxNum + 1, To: b}

			<-ticker.C
		}
	}()

	return now
}

func (e *evm) blockDispatch(ctx context.Context) {
	p, err := ants.NewPoolWithFunc(2, e.getBlockByNumber)
	if err != nil {
		panic(err)
	}

	defer p.Release()

	for {
		select {
		case <-ctx.Done():
			return
		case n := <-e.blockScanQueue.Out:
			if err := p.Invoke(n); err != nil {
				e.blockScanQueue.In <- n

				log.Warn("evmBlockDispatch Error invoking process block:", err)
			}
		}
	}
}

// getBlockByNumber 获取指定范围的区块信息并解析转账
func (e *evm) getBlockByNumber(blockData any) {
	blockRange, ok := blockData.(evmBlock)
	if !ok {
		log.Warn("getBlockByNumber 参数类型错误: 期望 evmBlock, 实际", blockData)
		return
	}

	// 构建批量区块请求
	batchPayload := e.buildBatchBlockRequest(blockRange)

	ctx, cancel := context.WithTimeout(context.Background(), httpRequestTimeout)
	defer cancel()

	// 发送批量请求获取区块信息
	body, err := e.sendBatchBlockRequest(ctx, batchPayload)
	if err != nil {
		e.handleBlockError(blockRange, "获取区块信息失败", err)
		return
	}

	// 解析区块响应并提取时间戳
	timestamps, err := e.extractBlockTimestamps(body)
	if err != nil {
		e.handleBlockError(blockRange, "解析区块时间戳失败", err)
		return
	}

	// 解析区块中的转账交易
	transfers, err := e.parseBlockTransfer(blockRange, timestamps)
	if err != nil {
		e.handleBlockError(blockRange, "解析区块转账失败", err)
		return
	}

	// 发送转账数据到处理队列
	if len(transfers) > 0 {
		transferQueue.In <- transfers
	}

	log.Info("区块扫描完成", blockRange, conf.GetBlockSuccRate(e.Network), e.Network)
}

// buildBatchBlockRequest 构建批量区块请求载荷
func (e *evm) buildBatchBlockRequest(blockRange evmBlock) []byte {
	requestCount := int(blockRange.To - blockRange.From + 1)
	requests := make([]string, 0, requestCount)

	for blockNum := blockRange.From; blockNum <= blockRange.To; blockNum++ {
		request := fmt.Sprintf(
			`{"jsonrpc":"2.0","method":"eth_getBlockByNumber","params":["0x%x",false],"id":%d}`,
			blockNum, blockNum,
		)
		requests = append(requests, request)
	}

	return []byte(fmt.Sprintf("[%s]", strings.Join(requests, ",")))
}

// sendBatchBlockRequest 发送批量区块请求
func (e *evm) sendBatchBlockRequest(ctx context.Context, payload []byte) ([]byte, error) {
	req := jsonRPCRequest{
		endpoint: e.Endpoint,
		payload:  payload,
		timeout:  httpRequestTimeout,
	}
	return makeJSONRPCRequest(ctx, req)
}

// extractBlockTimestamps 从区块响应中提取时间戳信息
func (e *evm) extractBlockTimestamps(responseBody []byte) (blockTimestamp, error) {
	timestamps := make(blockTimestamp)

	for _, blockItem := range gjson.ParseBytes(responseBody).Array() {
		if blockItem.Get("error").Exists() {
			return nil, fmt.Errorf("区块请求响应错误: %s", blockItem.Get("error").String())
		}

		blockNumber := blockItem.Get("result.number").String()
		timestampHex := blockItem.Get("result.timestamp").String()
		timestamp := time.Unix(help.HexStr2Int(timestampHex).Int64(), 0)

		timestamps[blockNumber] = timestamp
	}

	return timestamps, nil
}

// parseBlockTransfer 解析区块中的转账交易
func (e *evm) parseBlockTransfer(blockRange evmBlock, timestamps blockTimestamp) ([]transfer, error) {
	// 获取转账日志
	transferLogs, err := e.fetchTransferLogs(blockRange)
	if err != nil {
		return nil, err
	}

	// 解析转账事件
	transfers := make([]transfer, 0, len(transferLogs))
	for _, logItem := range transferLogs {
		if transferData := e.parseTransferEvent(logItem, timestamps); transferData != nil {
			transfers = append(transfers, *transferData)
		}
	}

	return transfers, nil
}

// fetchTransferLogs 获取指定区块范围的转账日志
func (e *evm) fetchTransferLogs(blockRange evmBlock) ([]gjson.Result, error) {
	payload := []byte(fmt.Sprintf(
		`{"jsonrpc":"2.0","method":"eth_getLogs","params":[{"fromBlock":"0x%x","toBlock":"0x%x","topics":["%s"]}],"id":1}`,
		blockRange.From, blockRange.To, evmTransferEvent,
	))

	resp, err := client.Post(e.Endpoint, httpContentType, bytes.NewBuffer(payload))
	if err != nil {
		return nil, fmt.Errorf("获取转账日志请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取转账日志响应失败: %w", err)
	}

	data := gjson.ParseBytes(body)
	if data.Get("error").Exists() {
		return nil, fmt.Errorf("转账日志响应错误: %s", data.Get("error").String())
	}

	return data.Get("result").Array(), nil
}

// parseTransferEvent 解析单个转账事件
func (e *evm) parseTransferEvent(logItem gjson.Result, timestamps blockTimestamp) *transfer {
	contractAddress := logItem.Get("address").String()
	tradeType, isSupported := contractMap[contractAddress]
	if !isSupported {
		return nil
	}

	topics := logItem.Get("topics").Array()
	if len(topics) < minTopicsLength || topics[0].String() != evmTransferEvent {
		return nil
	}

	// 解析转账金额
	amount, isValidAmount := parseHexAmount(logItem.Get("data").String())
	if !isValidAmount {
		return nil
	}

	// 解析区块号
	blockNumber, err := strconv.ParseInt(logItem.Get("blockNumber").String(), 0, 64)
	if err != nil {
		log.Warn(fmt.Sprintf("%s 解析区块号失败: %v", e.Network, err))
		return nil
	}

	// 构建转账数据
	return &transfer{
		Network:     e.Network,
		FromAddress: formatEvmAddress(topics[1].String()),
		RecvAddress: formatEvmAddress(topics[2].String()),
		Amount:      decimal.NewFromBigInt(amount, decimals[contractAddress]),
		TxHash:      logItem.Get("transactionHash").String(),
		BlockNum:    blockNumber,
		Timestamp:   timestamps[logItem.Get("blockNumber").String()],
		TradeType:   tradeType,
	}
}

// tradeConfirmHandle 处理交易确认
func (e *evm) tradeConfirmHandle(ctx context.Context) {
	orders := getConfirmingOrders(networkTokenMap[e.Network])
	if len(orders) == 0 {
		return
	}

	// 使用goroutine池处理交易确认
	pool, err := ants.NewPoolWithFunc(goroutinePoolSize, func(orderData interface{}) {
		if order, ok := orderData.(model.TradeOrders); ok {
			e.confirmSingleTransaction(ctx, order)
		}
	})
	if err != nil {
		log.Warn(fmt.Sprintf("%s 创建交易确认goroutine池失败: %v", e.Network, err))
		return
	}
	defer pool.Release()

	// 提交所有订单到池中处理
	var wg sync.WaitGroup
	for _, order := range orders {
		wg.Add(1)
		go func(o model.TradeOrders) {
			defer wg.Done()
			if err := pool.Invoke(o); err != nil {
				log.Warn(fmt.Sprintf("%s 提交交易确认任务失败: %v", e.Network, err))
			}
		}(order)
	}

	wg.Wait()
}

// confirmSingleTransaction 确认单个交易状态
func (e *evm) confirmSingleTransaction(ctx context.Context, order model.TradeOrders) {
	payload := []byte(fmt.Sprintf(
		`{"jsonrpc":"2.0","method":"eth_getTransactionReceipt","params":["%s"],"id":1}`,
		order.TradeHash,
	))

	req := jsonRPCRequest{
		endpoint: e.Endpoint,
		payload:  payload,
		timeout:  httpRequestTimeout,
	}

	body, err := makeJSONRPCRequest(ctx, req)
	if err != nil {
		log.Warn(fmt.Sprintf("%s 获取交易收据失败 [%s]: %v", e.Network, order.TradeHash, err))
		return
	}

	response := gjson.ParseBytes(body)
	if response.Get("error").Exists() {
		log.Warn(fmt.Sprintf("%s 交易收据响应错误 [%s]: %s",
			e.Network, order.TradeHash, response.Get("error").String()))
		return
	}

	// 检查交易状态是否成功
	if response.Get("result.status").String() == successTxStatus {
		markFinalConfirmed(order)
	}
}

func rollBreak(network string) bool {
	token, ok := networkTokenMap[network]
	if !ok {

		return true
	}

	var count int64 = 0
	model.DB.Model(&model.TradeOrders{}).Where("status = ? and trade_type in (?)", model.OrderStatusWaiting, token).Count(&count)
	if count > 0 {

		return false
	}

	model.DB.Model(&model.WalletAddress{}).Where("other_notify = ? and trade_type in (?)", model.OtherNotifyEnable, token).Count(&count)
	return count > 0
}
